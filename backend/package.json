{"name": "backend", "version": "1.0.0", "main": "index.js", "scripts": {"prod": "node ./dist/server.js", "dev": "ts-node-dev --respawn --transpile-only src/server.ts", "build": "tsc", "lint": "eslint src --ignore-path .eslint<PERSON>ore --ext .ts", "lint:fix": "npx eslint src --fix", "prettier": "prettier --ignore-path .gitignore --write \"./src/**/*.+(js|ts|json)\"", "prettier:fix": "npx prettier --write src", "seed": "ts-node src/scripts/seedDatabase.ts", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": [], "author": "", "license": "ISC", "description": "", "dependencies": {"@jest/globals": "^30.0.0", "@types/bcrypt": "^5.0.2", "@types/cookie-parser": "^1.4.8", "@types/express": "^5.0.1", "@types/fluent-ffmpeg": "^2.1.27", "@types/jsonwebtoken": "^9.0.9", "@types/mime-types": "^2.1.4", "@types/mongoose": "^5.11.97", "@types/multer": "^1.4.12", "@types/node": "22.15.3", "@types/nodemailer": "^6.4.17", "@types/stripe": "^8.0.417", "@typescript-eslint/eslint-plugin": "^8.30.1", "@typescript-eslint/parser": "^8.30.1", "agenda": "^5.0.0", "backend": ".", "bcrypt": "^5.1.1", "cloudinary": "^2.6.0", "cookie-parser": "^1.4.7", "cors": "^2.8.5", "date-fns": "^4.1.0", "dotenv": "^16.5.0", "eslint": "^9.24.0", "express": "^5.1.0", "fluent-ffmpeg": "^2.1.3", "http-status": "^2.1.0", "ioredis": "^5.6.1", "jsonwebtoken": "^9.0.2", "lint-staged": "^15.5.1", "mime-types": "^3.0.1", "mongoose": "^8.14.1", "multer": "^1.4.5-lts.2", "nodemailer": "^6.10.1", "passport": "^0.7.0", "passport-apple": "^2.0.2", "passport-facebook": "^3.0.0", "passport-google-oauth20": "^2.0.0", "prettier": "^3.5.3", "qrcode": "^1.5.4", "speakeasy": "^2.0.0", "stripe": "^18.1.0", "ts-node": "^10.9.2", "ts-node-dev": "^2.0.0", "zod": "^3.24.2"}, "devDependencies": {"@eslint/js": "^9.24.0", "@types/cors": "^2.8.17", "@types/passport": "^1.0.17", "@types/passport-apple": "^2.0.3", "@types/passport-facebook": "^3.0.3", "@types/passport-google-oauth20": "^2.0.16", "@types/qrcode": "^1.5.5", "@types/speakeasy": "^2.0.10", "@types/uuid": "^10.0.0", "globals": "^16.0.0", "typescript": "^5.8.3", "typescript-eslint": "^8.30.1"}}